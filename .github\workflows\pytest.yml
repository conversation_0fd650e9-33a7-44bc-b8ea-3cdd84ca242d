name: Tests on CI

on:
  pull_request:
  push:
    branches:
      - main

concurrency:
  group: ci-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  cpu:
    name: Tests on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.11"]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: "${{ matrix.python-version }}"
      - uses: actions/setup-go@v5
        with:
          go-version: '>=1.17.0'
      # Add Go bin directory to PATH for this workflow
      # GITHUB_PATH is a special file that GitHub Actions uses to modify PATH
      # Writing to this file adds the directory to the PATH for subsequent steps
      - name: Cache Go binaries
        id: cache-go-binaries
        uses: actions/cache@v3
        with:
          path: |
            ~/go/bin
            ~/.cache/go-build
          key: go-binaries-${{ runner.os }}-gopls-latest
      - name: Install gopls
        if: steps.cache-go-binaries.outputs.cache-hit != 'true'
        shell: bash
        run: go install golang.org/x/tools/gopls@latest
      - name: Set up Elixir
        if: runner.os != 'Windows'
        uses: erlef/setup-beam@v1
        with:
          elixir-version: '1.18.4'
          otp-version: '26.1'

      - name: Prepare java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"
      - name: Install clojure tools
        uses: DeLaGuardo/setup-clojure@13.4
        with:
          cli: latest
      - name: Install Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.5.0"
          terraform_wrapper: false
      - name: Install uv
        shell: bash
        run: curl -LsSf https://astral.sh/uv/install.sh | sh
      - name: Cache uv virtualenv
        id: cache-uv
        uses: actions/cache@v3
        with:
          path: .venv
          key: uv-venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('uv.lock') }}
      - name: Cache language servers
        id: cache-language-servers
        uses: actions/cache@v3
        with:
          path: ~/.serena/language_servers/static
          key: language-servers-${{ runner.os }}-v1
          restore-keys: |
            language-servers-${{ runner.os }}-
      - name: Create virtual environment
        shell: bash
        run: |
          if [ ! -d ".venv" ]; then
            uv venv
          fi
      - name: Install dependencies
        shell: bash
        run: uv pip install -e ".[dev]"
      - name: Test with pytest
        shell: bash
        run: uv run poe test
