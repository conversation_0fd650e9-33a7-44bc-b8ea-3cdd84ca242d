# Some of <PERSON>'s tools are just outputting a fixed text block without doing anything else.
# Such tools are meant to encourage the agent to think in a certain way, to stay on track
# and so on. The (templates for) outputs of these tools are contained here.
prompts:
  onboarding_prompt: |
    You are viewing the project for the first time.
    Your task is to assemble relevant high-level information about the project which
    will be saved to memory files in the following steps.
    The information should be sufficient to understand what the project is about,
    and the most important commands for developing code.
    The project is being developed on the system: {{ system }}.

    You need to identify at least the following information:
    * the project's purpose
    * the tech stack used
    * the code style and conventions used (including naming, type hints, docstrings, etc.)
    * which commands to run when a task is completed (linting, formatting, testing, etc.)
    * the rough structure of the codebase
    * the commands for testing, formatting, and linting
    * the commands for running the entrypoints of the project
    * the util commands for the system, like `git`, `ls`, `cd`, `grep`, `find`, etc. Keep in mind that the system is {{ system }},
      so the commands might be different than on a regular unix system.
    * whether there are particular guidelines, styles, design patterns, etc. that one should know about

    This list is not exhaustive, you can add more information if you think it is relevant.

    For doing that, you will need to acquire information about the project with the corresponding tools.
    Read only the necessary files and directories to avoid loading too much data into memory.
    If you cannot find everything you need from the project itself, you should ask the user for more information.

    After collecting all the information, you will use the `write_memory` tool (in multiple calls) to save it to various memory files.
    A particularly important memory file will be the `suggested_commands.md` file, which should contain
    a list of commands that the user should know about to develop code in this project.
    Moreover, you should create memory files for the style and conventions and a dedicated memory file for
    what should be done when a task is completed.
    **Important**: after done with the onboarding task, remember to call the `write_memory` to save the collected information!

  think_about_collected_information: |
    Have you collected all the information you need for solving the current task? If not, can the missing information be acquired by using the available tools,
    in particular the tools related to symbol discovery? Or do you need to ask the user for more information?
    Think about it step by step and give a summary of the missing information and how it could be acquired.

  think_about_task_adherence: |
    Are you deviating from the task at hand? Do you need any additional information to proceed?
    Have you loaded all relevant memory files to see whether your implementation is fully aligned with the
    code style, conventions, and guidelines of the project? If not, adjust your implementation accordingly
    before modifying any code into the codebase.
    Note that it is better to stop and ask the user for clarification
    than to perform large changes which might not be aligned with the user's intentions.
    If you feel like the conversation is deviating too much from the original task, apologize and suggest to the user
    how to proceed. If the conversation became too long, create a summary of the current progress and suggest to the user
    to start a new conversation based on that summary.

  think_about_whether_you_are_done: |
    Have you already performed all the steps required by the task? Is it appropriate to run tests and linting, and if so,
    have you done that already? Is it appropriate to adjust non-code files like documentation and config and have you done that already?
    Should new tests be written to cover the changes?
    Note that a task that is just about exploring the codebase does not require running tests or linting.
    Read the corresponding memory files to see what should be done when a task is completed. 

  summarize_changes: |
    Summarize all the changes you have made to the codebase over the course of the conversation.
    Explore the diff if needed (e.g. by using `git diff`) to ensure that you have not missed anything.
    Explain whether and how the changes are covered by tests. Explain how to best use the new code, how to understand it,
    which existing code it affects and interacts with. Are there any dangers (like potential breaking changes or potential new problems) 
    that the user should be aware of? Should any new documentation be written or existing documentation updated?
    You can use tools to explore the codebase prior to writing the summary, but don't write any new code in this step until
    the summary is complete.

  prepare_for_new_conversation: |
    You have not yet completed the current task but we are running out of context.
    {mode_prepare_for_new_conversation}
    Imagine that you are handing over the task to another person who has access to the
    same tools and memory files as you do, but has not been part of the conversation so far.
    Write a summary that can be used in the next conversation to a memory file using the `write_memory` tool.
