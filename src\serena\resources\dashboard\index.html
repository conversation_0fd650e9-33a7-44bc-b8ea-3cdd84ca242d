<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Serena Dashboard</title>
    <link rel="icon" type="image/png" sizes="16x16" href="serena-icon-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="serena-icon-32.png">
    <link rel="icon" type="image/png" sizes="48x48" href="serena-icon-48.png">
    <script src="jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
    <script src="dashboard.js"></script>
    <style>
        body {
            font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .log-container {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            overflow-x: auto;
            padding: 10px;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .controls {
            margin-bottom: 10px;
            text-align: center;
        }

        .logo {
            margin-bottom: 10px;
            text-align: center;
        }

        .btn {
            background-color: #eaa45d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #dca662;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log-debug {
            color: #808080; /* Gray */
        }
        .log-info {
            color: #000000; /* Black */
        }
        .log-warning {
            color: #FF8C00; /* Dark Orange */
        }
        .log-error {
            color: #FF0000; /* Red */
        }
        .log-default {
            color: #000000; /* Black */
        }
        
        /* Tool name highlighting */
        .tool-name {
            background-color: #ffff00; /* Yellow background */
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .error-message {
            color: #FF0000;
            text-align: center;
            margin: 10px 0;
        }
        
        .charts-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .chart-group {
            flex: 1;
            min-width: 280px;
            max-width: 320px;
            text-align: center;
        }
        
        .chart-wide {
            flex: 0 0 100%;
            min-width: 100%;
            margin-top: 10px;
        }
        
        .chart-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .stats-summary {
            margin: 0 auto;
            border-collapse: collapse;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats-summary th,
        .stats-summary td {
            padding: 10px 20px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .stats-summary th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .stats-summary tr:last-child td {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .charts-container {
                flex-direction: column;
            }
            
            .chart-group,
            .chart-wide {
                min-width: auto;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="serena-logs.png" alt="Serena" style="max-width: 400px; height: auto;">
    </div>
    
    <div class="controls">
        <button id="load-logs" class="btn">Reload Log</button>
        <button id="shutdown" class="btn">Shutdown Server</button>
        <button id="toggle-stats" class="btn">Show Stats</button>
    </div>

    <div id="error-container"></div>
    <div id="log-container" class="log-container"></div>

    <div id="stats-section" style="display:none; margin-top:20px;">
        <div style="text-align:center; margin-bottom:20px;">
            <button id="refresh-stats" class="btn">Refresh Stats</button>
            <button id="clear-stats" class="btn">Clear Stats</button>
        </div>
        
        <div id="stats-summary" style="margin-bottom:20px; text-align:center;"></div>
        <div id="estimator-name" style="text-align:center; margin-bottom:10px;"></div>
        <div id="no-stats-message" style="text-align:center; color:#666; font-style:italic; display:none;">
          No tool stats collected. Have you enabled tool stats collection in the configuration?
        </div>

        
        <div class="charts-container">
            <div class="chart-group">
                <h3>Tool Calls</h3>
                <canvas id="count-chart" height="200"></canvas>
            </div>
            <div class="chart-group">
                <h3>Input Tokens</h3>
                <canvas id="input-chart" height="200"></canvas>
            </div>
            <div class="chart-group">
                <h3>Output Tokens</h3>
                <canvas id="output-chart" height="200"></canvas>
            </div>
            <div class="chart-group chart-wide">
                <h3>Input vs Output Tokens</h3>
                <canvas id="tokens-chart" height="120"></canvas>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            const dashboard = new Dashboard();
        });
    </script>
</body>
</html>
