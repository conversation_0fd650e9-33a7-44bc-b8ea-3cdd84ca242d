description: Only read-only tools, focused on analysis and planning
prompt: |
  You are operating in planning mode. Your task is to analyze code but not write any code.
  The user may ask you to assist in creating a comprehensive plan, or to learn something about the codebase -
  either a small aspect of it or about the whole project.
excluded_tools:
  - create_text_file
  - replace_symbol_body
  - insert_after_symbol
  - insert_before_symbol
  - delete_lines
  - replace_lines
  - insert_at_line
  - execute_shell_command
  - replace_regex
